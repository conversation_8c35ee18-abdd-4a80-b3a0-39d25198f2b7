import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { withSetPropAction } from "../helpers/withSetPropAction";
import { MerchantModel } from "app/models"

export const PricingTierModel = types
  .model("PricingTier")
  .props({
    id: types.identifier,
    name: types.string,
    cost: types.number,
    currency: types.string,
    description: types.maybeNull(types.string),
    billingPeriod: types.optional(types.string, "Monthly"),
    trialPeriodDays: types.maybeNull(types.number),
    idealFor: types.array(types.string),
    updatedAt: types.string,
  })
  .actions(withSetPropAction);

// @ts-ignore
export const ProductModel = types
  .model("Product")
  .props({
    id: types.identifier,
    merchant: types.safeReference(types.late(() => MerchantModel)),
    name: types.string,
    description: types.string,
    idealFor: types.array(types.string),
    pricingTiers: types.optional(types.array(PricingTierModel), []),
    updatedAt: types.string,
  })
  .actions(withSetPropAction)
  .views((self) => ({
    pricingTierCount: () => self.pricingTiers.length,
  }))
  .actions((self) => ({
    setMerchant(merchant: Instance<typeof MerchantModel>) {
      self.merchant = merchant;
    },
    addPricingTier(tier: PricingTierSnapshotIn) {
        self.pricingTiers.push(tier);
    },
    removePricingTierById(tierId: string) {
      self.pricingTiers.replace(self.pricingTiers.filter((t) => t.id !== tierId));
    },
  }));

export interface PricingTier extends Instance<typeof PricingTierModel> {}
export interface PricingTierSnapshotOut extends SnapshotOut<typeof PricingTierModel> {}
export interface PricingTierSnapshotIn extends SnapshotIn<typeof PricingTierModel> {}
export const createPricingTierDefaultModel = () =>
  types.optional(PricingTierModel, {
    id: "",
    name: "",
    cost: 0,
    currency: "",
    description: null,
    billingPeriod: "Monthly",
    trialPeriodDays: null,
    idealFor: [],
    updatedAt: "",
  });

export interface Product extends Instance<typeof ProductModel> {}
export interface ProductSnapshotOut extends SnapshotOut<typeof ProductModel> {}
export interface ProductSnapshotIn extends SnapshotIn<typeof ProductModel> {}
export const createProductDefaultModel = () =>
  types.optional(ProductModel, {
    id: "",
    name: "",
    merchant:"",
    description: "",
    idealFor: [],
    pricingTiers: [],
    updatedAt: "",
  });
