import React, { FC, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, TextStyle, ScrollView, View, Platform } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"

interface UserAnalyticsScreenProps extends AppStackScreenProps<"UserAnalytics"> {}

export const UserAnalyticsScreen: FC<UserAnalyticsScreenProps> = observer(function UserAnalyticsScreen() {
  const rootStore = useStores()
  const { userId } = useAuth()

  // Fetch subscriptions if needed
  useEffect(() => {
    if (userId) {
      if (!rootStore.isSubscriptionsCacheValid || rootStore.subscriptions.length === 0) {
        if (__DEV__ && console.tron) {
          console.tron.log('📊 Analytics screen loading subscriptions')
        }
        rootStore.fetchUserSubscriptions(userId)
      }
    }
  }, [userId, rootStore.isSubscriptionsCacheValid])

  // Prepare JSON data for display
  const getSubscriptionsJSON = () => {
    try {
      const subscriptionsData = {
        metadata: {
          totalSubscriptions: rootStore.subscriptions.length,
          cacheValid: rootStore.isSubscriptionsCacheValid,
          lastRefreshTime: rootStore.subscriptionsLastRefreshTime,
          loading: rootStore.subscriptionsLoading,
          timestamp: new Date().toISOString()
        },
        rawSubscriptions: rootStore.subscriptions.map(sub => ({
          id: sub.id,
          userId: sub.userId,
          productId: sub.productId,
          pricingTierId: sub.pricingTierId,
          startedOn: sub.startedOn,
          endedOn: sub.endedOn,
          billDate: sub.billDate,
          createdAt: sub.createdAt,
          updatedAt: sub.updatedAt,
          // Computed properties
          isActive: sub.isActive,
          merchantName: sub.merchantName,
          merchantLogo: sub.merchantLogo,
          merchantCategory: sub.merchantCategory,
          productName: sub.productName,
          cost: sub.cost,
          currency: sub.currency
        })),
        groupedSubscriptions: rootStore.groupedSubscriptions,
        relatedData: {
          merchantGroups: rootStore.merchantGroups.length,
          merchants: rootStore.merchants.length,
          products: rootStore.products.length
        }
      }

      return JSON.stringify(subscriptionsData, null, 2)
    } catch (error) {
      if (__DEV__ && console.tron) {
        console.tron.error('❌ Error generating subscriptions JSON:', error)
      }
      return JSON.stringify({ error: 'Failed to generate JSON data' }, null, 2)
    }
  }

  return (
    <Screen style={$root} preset="fixed">
      <ScrollView style={$scrollView} contentContainerStyle={$scrollContainer}>
        <View style={$headerContainer}>
          <Text style={$title}>Analytics - MST Data Dump</Text>
          <Text style={$subtitle}>
            All subscription data from MST store as JSON
          </Text>
        </View>

        <View style={$jsonContainer}>
          <ScrollView
            style={$jsonScrollView}
            contentContainerStyle={$jsonContent}
            showsVerticalScrollIndicator={true}
            nestedScrollEnabled={true}
          >
            <Text style={$jsonText}>
              {getSubscriptionsJSON()}
            </Text>
          </ScrollView>
        </View>
      </ScrollView>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $scrollView: ViewStyle = {
  flex: 1,
}

const $scrollContainer: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.xl,
}

const $headerContainer: ViewStyle = {
  paddingVertical: spacing.lg,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
  marginBottom: spacing.md,
}

const $title: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
  fontFamily: typography.primary.bold,
  color: colors.text,
  marginBottom: spacing.xs,
}

const $subtitle: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}

const $jsonContainer: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.separator,
  minHeight: 400,
}

const $jsonScrollView: ViewStyle = {
  flex: 1,
  maxHeight: 600, // Limit height to prevent excessive scrolling
}

const $jsonContent: ViewStyle = {
  padding: spacing.md,
}

const $jsonText: TextStyle = {
  fontSize: 12,
  lineHeight: 16,
  color: colors.text,
  fontFamily: typography.code.normal,
}
