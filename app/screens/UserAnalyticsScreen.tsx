import React, { FC, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, TextStyle, ScrollView, View, Platform } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"

interface UserAnalyticsScreenProps extends AppStackScreenProps<"UserAnalytics"> {}

export const UserAnalyticsScreen: FC<UserAnalyticsScreenProps> = observer(function UserAnalyticsScreen() {
  const rootStore = useStores()
  const { userId } = useAuth()

  // Fetch subscriptions if needed
  useEffect(() => {
    if (userId) {
      if (!rootStore.isSubscriptionsCacheValid || rootStore.subscriptions.length === 0) {
        if (__DEV__ && console.tron) {
          console.tron.log('📊 Analytics screen loading subscriptions')
        }
        rootStore.fetchUserSubscriptions(userId)
      }
    }
  }, [userId, rootStore.isSubscriptionsCacheValid])

  // Prepare subscriptions sorted by next bill date
  const getSubscriptionsByBillDate = () => {
    return rootStore.subscriptions
      .filter(sub => sub.nextBillDate) // Only include subscriptions with next bill dates
      .slice() // Create a copy to avoid mutating the original array
      .sort((a, b) => {
        const dateA = new Date(a.nextBillDate!)
        const dateB = new Date(b.nextBillDate!)
        return dateA.getTime() - dateB.getTime()
      })
  }

  const formatBillDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'EUR'
    }).format(amount)
  }

  // Calculate monthly total
  const getMonthlyTotal = () => {
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()

    const monthlySubscriptions = rootStore.subscriptions.filter(sub => {
      if (!sub.billDate) return false
      const billDate = new Date(sub.billDate)
      return billDate.getMonth() === currentMonth && billDate.getFullYear() === currentYear
    })

    const total = monthlySubscriptions.reduce((sum, sub) => sum + sub.cost, 0)
    const currency = monthlySubscriptions.length > 0 ? monthlySubscriptions[0].currency : 'EUR'

    return { total, currency, count: monthlySubscriptions.length }
  }

  const monthlyData = getMonthlyTotal()
  const currentMonthName = new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })

  return (
    <Screen style={$root} preset="fixed">
      <ScrollView contentContainerStyle={$scrollContainer}>
        {/* Monthly Total Header */}
        <View style={$monthlyTotalContainer}>
          <Text style={$monthlyTotalLabel}>{currentMonthName} Total</Text>
          <Text style={$monthlyTotalAmount}>
            {formatCurrency(monthlyData.total, monthlyData.currency)}
          </Text>
          <Text style={$monthlyTotalCount}>
            {monthlyData.count} subscription{monthlyData.count !== 1 ? 's' : ''}
          </Text>
        </View>

        {/* Subscription List */}
        <View style={$listContainer}>
          {getSubscriptionsByBillDate().map((subscription, index) => (
            <View key={subscription.id} style={$listItem}>
              <View style={$listItemContent}>
                <View style={$listItemLeft}>
                  <Text style={$merchantName} numberOfLines={1}>
                    {subscription.merchantName}
                  </Text>
                  <Text style={$productName} numberOfLines={1}>
                    {subscription.productName}
                  </Text>
                </View>

                <View style={$listItemRight}>
                  <Text style={$costAmount}>
                    {formatCurrency(subscription.cost, subscription.currency)}
                  </Text>
                  <Text style={$billDate}>
                    {formatBillDate(subscription.billDate!)}
                  </Text>
                </View>
              </View>
            </View>
          ))}

          {getSubscriptionsByBillDate().length === 0 && (
            <View style={$emptyState}>
              <Text style={$emptyStateText}>No upcoming bills found</Text>
              <Text style={$emptyStateSubtext}>
                Add bill dates to your subscriptions to see them here
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $scrollContainer: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.xl,
}

// Monthly Total Styles
const $monthlyTotalContainer: ViewStyle = {
  backgroundColor: colors.palette.primary100,
  borderRadius: 12,
  padding: spacing.lg,
  marginBottom: spacing.lg,
  alignItems: 'center',
  borderWidth: 1,
  borderColor: colors.palette.primary200,
}

const $monthlyTotalLabel: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.medium,
  color: colors.palette.primary600,
  marginBottom: spacing.xs,
}

const $monthlyTotalAmount: TextStyle = {
  fontSize: 32,
  fontWeight: "bold",
  fontFamily: typography.primary.bold,
  color: colors.palette.primary600,
  marginBottom: spacing.xxs,
}

const $monthlyTotalCount: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.palette.primary500,
}

// List Styles
const $listContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
  overflow: 'hidden',
}

const $listItem: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
}

const $listItemContent: ViewStyle = {
  flexDirection: 'row',
  padding: spacing.md,
  alignItems: 'center',
  justifyContent: 'space-between',
}

const $listItemLeft: ViewStyle = {
  flex: 1,
  marginRight: spacing.md,
}

const $listItemRight: ViewStyle = {
  alignItems: 'flex-end',
  minWidth: 100,
}

const $merchantName: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: spacing.xxs,
}

const $productName: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}

const $costAmount: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  fontFamily: typography.primary.semiBold,
  color: colors.palette.primary600,
  marginBottom: spacing.xxs,
}

const $billDate: TextStyle = {
  fontSize: 13,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}

// Empty State Styles
const $emptyState: ViewStyle = {
  padding: spacing.xl,
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: colors.palette.neutral100,
  borderRadius: 12,
}

const $emptyStateText: TextStyle = {
  fontSize: 18,
  fontWeight: "600",
  fontFamily: typography.primary.semiBold,
  color: colors.textDim,
  marginBottom: spacing.xs,
  textAlign: 'center',
}

const $emptyStateSubtext: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  textAlign: 'center',
  lineHeight: 20,
}
